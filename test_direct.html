<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Test - Wiring Minigame</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #222;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .test-controls {
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 1000;
            background: rgba(0,0,0,0.8);
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #555;
        }
        
        .test-controls button {
            padding: 8px 15px;
            margin: 3px;
            background: #0066FF;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .test-controls button:hover {
            background: #0055DD;
        }
        
        .test-controls input, .test-controls select {
            padding: 5px;
            margin: 2px;
            border: 1px solid #555;
            background: #444;
            color: white;
            border-radius: 3px;
            font-size: 12px;
            width: 60px;
        }
        
        .test-controls label {
            font-size: 12px;
            margin-right: 5px;
        }
        
        .control-row {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <!-- Test ovládání -->
    <div class="test-controls">
        <div class="control-row">
            <label>Kabely:</label>
            <input type="number" id="wireCount" min="1" max="8" value="4">
            <label>Barvy:</label>
            <select id="colors">
                <option value="classic" selected>Classic</option>
                <option value="extended">Extended</option>
                <option value="rainbow">Rainbow</option>
            </select>
        </div>
        <div class="control-row">
            <button onclick="startTest()">▶️ Start</button>
            <button onclick="stopTest()">⏹️ Stop</button>
            <button onclick="resetTest()">🔄 Reset</button>
        </div>
    </div>

    <!-- Načtení obsahu z simple_enhanced.html -->
    <script>
        // Načtení HTML obsahu
        fetch('html/simple_enhanced.html')
            .then(response => response.text())
            .then(html => {
                // Extrakce body obsahu
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                
                // Přidání stylů
                const styles = doc.querySelectorAll('style');
                styles.forEach(style => {
                    document.head.appendChild(style.cloneNode(true));
                });
                
                // Přidání body obsahu
                const appDiv = doc.querySelector('#app');
                if (appDiv) {
                    document.body.appendChild(appDiv.cloneNode(true));
                }
                
                // Přidání skriptů
                const scripts = doc.querySelectorAll('script');
                scripts.forEach(script => {
                    if (script.src) {
                        const newScript = document.createElement('script');
                        newScript.src = script.src;
                        document.head.appendChild(newScript);
                    } else if (script.textContent) {
                        const newScript = document.createElement('script');
                        newScript.textContent = script.textContent;
                        document.head.appendChild(newScript);
                    }
                });
                
                // Čekání na načtení GSAP a spuštění testu
                setTimeout(() => {
                    startTest();
                }, 2000);
            })
            .catch(error => {
                console.error('Chyba při načítání:', error);
                document.body.innerHTML = '<h1 style="color: red; text-align: center; margin-top: 50px;">Chyba při načítání minihry!</h1>';
            });
        
        function startTest() {
            const wireCount = parseInt(document.getElementById('wireCount').value);
            const colors = document.getElementById('colors').value;
            
            const message = {
                ui: "ui",
                NuiOpen: true,
                x: "50%",
                y: "50%",
                scale: 1.0,
                size_game: "907px",
                name_resource: "DVRP_minigame",
                sound_name: "1.ogg",
                wire_count: wireCount,
                colors: colors,
                snap_radius: 25
            };
            
            // Simulace zprávy z FiveM
            window.postMessage(message, '*');
            console.log('Test spuštěn s nastavením:', message);
        }
        
        function stopTest() {
            const message = {
                ui: "ui",
                NuiOpen: false
            };
            
            window.postMessage(message, '*');
            console.log('Test zastaven');
        }
        
        function resetTest() {
            stopTest();
            setTimeout(startTest, 500);
        }
        
        // Klávesové zkratky
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F5') {
                e.preventDefault();
                resetTest();
            }
        });
    </script>
</body>
</html>
