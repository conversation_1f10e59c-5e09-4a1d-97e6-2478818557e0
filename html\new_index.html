<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="icon" href="favicon.ico">
    <title>Enhanced Wiring Minigame</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@100&family=Poppins:wght@100&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.11.2/css/all.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@100&display=swap" rel="stylesheet">
    
    <style>
        body {
            margin: 0;
            padding: 0;
            background: transparent;
            font-family: 'Roboto', sans-serif;
            overflow: hidden;
        }
        
        #app {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        #container {
            position: absolute;
            user-select: none;
        }
        
        .game-svg {
            background: linear-gradient(135deg, #1e2021 0%, #272726 100%);
            border: 14px solid #000;
            border-radius: 10px;
        }
        
        .wire-drag {
            cursor: grab;
            transition: all 0.2s ease;
        }
        
        .wire-drag:hover {
            filter: brightness(1.2);
            transform: scale(1.05);
        }
        
        .wire-drag:active {
            cursor: grabbing;
        }
        
        .wire-line {
            pointer-events: none;
            filter: drop-shadow(0 0 3px rgba(0,0,0,0.5));
        }
        
        .wire-target {
            pointer-events: none;
            stroke: #fff;
            stroke-width: 2;
            stroke-dasharray: 5,5;
            animation: dash 1s linear infinite;
        }
        
        @keyframes dash {
            to {
                stroke-dashoffset: -10;
            }
        }
        
        .light {
            filter: drop-shadow(0 0 10px currentColor);
            transition: opacity 0.3s ease;
        }
        
        .light.active {
            animation: pulse 0.5s ease-in-out;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        /* Responsive design for different wire counts */
        .wire-container-small .wire-drag {
            /* For 1-4 wires */
            min-height: 35px;
        }
        
        .wire-container-medium .wire-drag {
            /* For 5-8 wires */
            min-height: 30px;
        }
        
        .wire-container-large .wire-drag {
            /* For 9-16 wires */
            min-height: 25px;
        }
        
        .wire-container-xlarge .wire-drag {
            /* For 17-32 wires */
            min-height: 20px;
        }
        
        /* Background circuit pattern */
        .circuit-bg {
            fill: none;
            stroke: #333;
            stroke-width: 1;
            opacity: 0.3;
        }
        
        /* Success animation */
        .success-flash {
            animation: successFlash 0.5s ease-in-out;
        }
        
        @keyframes successFlash {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; filter: brightness(1.5); }
        }
    </style>
</head>
<body>
    <div id="app">
        <div v-if="NuiOpen" :style="position_nui" id="container">
            <svg 
                :style="size_game" 
                :width="gameWidth" 
                :height="gameHeight" 
                :viewBox="`0 0 ${gameWidth} ${gameHeight}`"
                class="game-svg"
                :class="wireContainerClass"
            >
                <!-- Background gradient -->
                <defs>
                    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#1d1d1b"/>
                        <stop offset="50%" style="stop-color:#272726"/>
                        <stop offset="100%" style="stop-color:#1d1d1b"/>
                    </linearGradient>
                    
                    <!-- Wire glow effects -->
                    <filter id="wireGlow">
                        <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
                        <feMerge> 
                            <feMergeNode in="coloredBlur"/>
                            <feMergeNode in="SourceGraphic"/>
                        </feMerge>
                    </filter>
                </defs>
                
                <!-- Background -->
                <rect width="100%" height="100%" fill="url(#bgGradient)"/>
                
                <!-- Circuit background pattern -->
                <g class="circuit-bg">
                    <path v-for="line in backgroundLines" :key="line.id" :d="line.path"/>
                </g>
                
                <!-- Left side connection points -->
                <g class="left-connections">
                    <rect 
                        v-for="(pos, index) in wirePositions.leftSide" 
                        :key="`left-${index}`"
                        :x="pos.x - 10" 
                        :y="pos.y - 5" 
                        :width="pos.width + 20" 
                        :height="pos.height + 10"
                        fill="#393e42"
                        stroke="#555"
                        stroke-width="2"
                        rx="5"
                    />
                </g>
                
                <!-- Right side connection points -->
                <g class="right-connections">
                    <rect 
                        v-for="(pos, index) in wirePositions.rightSide" 
                        :key="`right-${index}`"
                        :x="pos.x - 10" 
                        :y="pos.y - 5" 
                        :width="pos.width + 20" 
                        :height="pos.height + 10"
                        fill="#393e42"
                        stroke="#555"
                        stroke-width="2"
                        rx="5"
                    />
                </g>
                
                <!-- Wire lines (drawn behind draggable elements) -->
                <g class="wire-lines">
                    <line 
                        v-for="(wire, index) in wires" 
                        :key="`line-${wire.id}`"
                        :class="`wire-line line-${wire.id}`"
                        :x1="wire.startPos.x + 30"
                        :y1="wire.startPos.y + 20"
                        :x2="wire.startPos.x + 30"
                        :y2="wire.startPos.y + 20"
                        :stroke="wire.color"
                        stroke-width="4"
                        filter="url(#wireGlow)"
                    />
                </g>
                
                <!-- Target areas (visual guides) -->
                <g class="target-areas">
                    <rect 
                        v-for="(wire, index) in wires" 
                        :key="`target-${wire.id}`"
                        :class="`wire-target target-${wire.id}`"
                        :x="wire.targetPos.x"
                        :y="wire.targetPos.y"
                        :width="wire.targetPos.width"
                        :height="wire.targetPos.height"
                        :fill="wire.color"
                        fill-opacity="0.2"
                        rx="3"
                    />
                </g>
                
                <!-- Draggable wire elements -->
                <g class="draggable-wires">
                    <rect 
                        v-for="(wire, index) in wires" 
                        :key="`drag-${wire.id}`"
                        :class="`wire-drag drag-${wire.id}`"
                        :x="wire.startPos.x"
                        :y="wire.startPos.y"
                        :width="wire.startPos.width"
                        :height="wire.startPos.height"
                        :fill="wire.color"
                        stroke="#fff"
                        stroke-width="2"
                        rx="3"
                        filter="url(#wireGlow)"
                    />
                </g>
                
                <!-- Light indicators -->
                <g class="lights">
                    <rect 
                        v-for="(wire, index) in wires" 
                        :key="`light-${wire.id}`"
                        :class="`light light-${wire.targetLight}`"
                        :x="gameWidth - 60"
                        :y="wire.targetPos.y + 5"
                        width="50"
                        height="24"
                        fill="#ffff00"
                        opacity="0"
                        rx="12"
                        stroke="#ffd700"
                        stroke-width="2"
                    />
                </g>
                
                <!-- Wire count indicator -->
                <text 
                    :x="gameWidth / 2" 
                    y="30" 
                    text-anchor="middle" 
                    fill="#fff" 
                    font-size="16" 
                    font-family="Roboto"
                    opacity="0.7"
                >
                    {{ wire_count }} Wires - Connect matching colors
                </text>
            </svg>
        </div>
    </div>

    <!-- Vue.js and dependencies will be loaded here -->
    <script src="js/chunk-vendors.d22fa5dc.js"></script>
    <script src="js/enhanced_app.js"></script>
</body>
</html>
