# Vylep<PERSON><PERSON><PERSON> minihry spojování kabelů

## Provedené změny

### 1. 🔧 Upravena tloušťka kabelů při přetahování

**Problém:** <PERSON><PERSON><PERSON> měly st<PERSON>le <PERSON> tlou<PERSON>, co<PERSON> nebylo viz<PERSON> jasn<PERSON>, kter<PERSON> kabel se právě přetahuje.

**Řešení:**
- Přidány CSS třídy `.dragging` pro kabely a linie
- Kabel při přeta<PERSON>í má nyní tloušťku `6px` místo `3px`
- <PERSON><PERSON> kabelu při přetahování má tloušťku `12px` místo `8px`
- Přidány vizuální efekty (silnější stíny a jas)

**Implementace:**
```css
.wire-drag.dragging {
    stroke-width: 6px;
    filter: drop-shadow(0 0 12px rgba(0,0,0,0.9)) brightness(1.5);
}

.wire-line.dragging {
    stroke-width: 12px;
    filter: drop-shadow(0 0 10px rgba(0,0,0,0.8));
}
```

### 2. 🎯 Přidány znaky pro barvoslepé hráče

**Problém:** Barvoslepí hráči měli problém rozlišit kabely pouze podle barvy.

**Řešení:**
- Přidán seznam 32 různých symbolů pro kabely
- Každý kabel má nyní jedinečný symbol (●, ■, ▲, ♦, ★, atd.)
- Symboly se zobrazují jak na draggable kabelech, tak na target oblastech
- Symboly mají bílou barvu s černým stínem pro lepší čitelnost

**Implementace:**
```javascript
const WIRE_SYMBOLS = [
    "●", "■", "▲", "♦", "★", "◆", "▼", "♠",
    "♣", "♥", "◊", "○", "□", "△", "☆", "◇",
    "♪", "♫", "※", "⚡", "⚙", "⬟", "⬢", "⬡",
    "⬠", "⬜", "⬛", "⭐", "⭕", "⚫", "⚪", "🔸"
];
```

### 3. 🎯 Opraveno přesné propojení kabelů s UI

**Problém:** Kabely se propojovaly o trochu pod ikonkami v UI.

**Řešení:**
- Opraveny výpočty pozic pro středy kabelů a target oblastí
- Linie nyní vychází ze středu draggable kabelu
- Při správném připojení se linie napojuje na střed target oblasti
- Symboly se pohybují společně s kabely a správně se pozicují

**Klíčové změny:**
```javascript
// Výchozí pozice linie ze středu kabelu
x1: wire.startPos.x + wire.startPos.width / 2,
y1: wire.startPos.y + wire.startPos.height / 2,

// Při připojení na střed target oblasti
updateLine(wireId, targetRelativeX + wire.targetPos.width / 2,
          targetRelativeY + wire.targetPos.height / 2);
```

### 4. 🎮 Vylepšená interakce při přetahování

**Změny:**
- Přidán `onDragStart` callback pro aktivaci vizuálních efektů
- Symboly se pohybují společně s kabely během přetahování
- Při uvolnění se všechny vizuální efekty správně resetují
- Lepší synchronizace mezi kabelem, linií a symbolem

### 5. 🧪 Přidáno testovací rozhraní

**Pro snadnější testování:**
- Přidáno testovací tlačítko přímo do HTML souboru
- Možnost rychle spustit/zastavit minihru
- Nastavitelný počet kabelů pro testování
- Automatické zobrazení po načtení GSAP knihoven

## Technické detaily

### Nové CSS třídy:
- `.wire-drag.dragging` - tlustší kabel při přetahování
- `.wire-line.dragging` - tlustší linie při přetahování  
- `.wire-symbol` - styly pro symboly na kabelech
- `.wire-symbol-target` - styly pro symboly na target oblastech

### Nové JavaScript funkce:
- Rozšířena `generateRandomConfiguration()` o přidání symbolů
- Upravena `generateSVGContent()` pro vytváření symbolů
- Vylepšena `setupDraggables()` s `onDragStart` callbackem
- Rozšířena `resetWire()` o reset pozice symbolů

### Vylepšené pozicování:
- Všechny pozice nyní počítají se středy elementů
- Přesnější detekce kolizí
- Lepší vizuální propojení kabelů

## Výsledek

✅ **Tloušťka kabelů** - Kabel při přetahování je nyní výrazně tlustší  
✅ **Znaky pro barvoslepé** - Každý kabel má jedinečný symbol  
✅ **Přesné propojení** - Kabely se propojují přesně se středy UI ikonek  
✅ **Lepší UX** - Celkově vylepšená uživatelská zkušenost  

Všechny změny jsou zpětně kompatibilní a neovlivňují stávající funkčnost minihry.
