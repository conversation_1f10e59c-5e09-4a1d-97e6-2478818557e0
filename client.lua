-- ===========================
-- GLOBÁLNÍ PROMĚNNÉ
-- ===========================

local isMinigameActive = false
local currentCallbacks = {}
local nuiReady = false

-- ===========================
-- UTILITY FUNKCE
-- ===========================

-- Debug print funkce
local function debugPrint(message)
    if Config.Debug then
        print("[WiringMinigame] " .. tostring(message))
    end
end

-- Získání pozice hráče pro centrování UI
local function getScreenPosition()
    local x = Config.Position.x
    local y = Config.Position.y
    
    -- Převod procent na pixely pokud je potřeba
    if type(x) == "string" and string.find(x, "%%") then
        x = string.gsub(x, "%%", "")
        x = tonumber(x) or 50
        x = x .. "%"
    end
    
    if type(y) == "string" and string.find(y, "%%") then
        y = string.gsub(y, "%%", "")
        y = tonumber(y) or 50
        y = y .. "%"
    end
    
    return x, y
end

-- Merge tabulek
local function mergeTables(t1, t2)
    local result = {}
    for k, v in pairs(t1 or {}) do
        result[k] = v
    end
    for k, v in pairs(t2 or {}) do
        result[k] = v
    end
    return result
end

-- ===========================
-- NUI KOMUNIKACE
-- ===========================

-- Spuštění minihry
local function startMinigame(options)
    if isMinigameActive then
        debugPrint("Minigame is already active!")
        return false
    end
    
    -- Sloučení s výchozím nastavením
    local gameOptions = mergeTables({
        wire_count = Config.WireCount,
        colors = Config.Colors,
        snap_radius = Config.SnapRadius,
        size_game = Config.GameSize,
        scale = Config.Scale,
        sound_name = Config.EnableSounds and Config.CompletionSound or "",
        name_resource = Config.ResourceName
    }, options or {})
    
    -- Získání pozice
    local x, y = getScreenPosition()
    gameOptions.x = x
    gameOptions.y = y
    
    debugPrint("Starting minigame with options: " .. json.encode(gameOptions))
    
    -- Nastavení stavu
    isMinigameActive = true
    
    -- Zobrazení kurzoru
    SetNuiFocus(true, true)
    
    -- Odeslání dat do NUI
    SendNUIMessage({
        ui = "ui",
        NuiOpen = true,
        wire_count = gameOptions.wire_count,
        colors = gameOptions.colors,
        snap_radius = gameOptions.snap_radius,
        x = gameOptions.x,
        y = gameOptions.y,
        scale = gameOptions.scale,
        size_game = gameOptions.size_game,
        sound_name = gameOptions.sound_name,
        name_resource = gameOptions.name_resource
    })
    
    return true
end

-- Zastavení minihry
local function stopMinigame()
    if not isMinigameActive then
        return false
    end
    
    debugPrint("Stopping minigame")
    
    -- Skrytí UI
    SendNUIMessage({
        ui = "ui",
        NuiOpen = false
    })
    
    -- Skrytí kurzoru
    SetNuiFocus(false, false)
    
    -- Reset stavu
    isMinigameActive = false
    
    -- Volání callback funkce
    if currentCallbacks.onClose then
        currentCallbacks.onClose(PlayerId())
    end
    
    currentCallbacks = {}
    
    return true
end

-- ===========================
-- NUI CALLBACKS
-- ===========================

-- Callback pro dokončení minihry
RegisterNUICallback('electric_circuit_completed', function(data, cb)
    debugPrint("Minigame completed successfully!")
    
    -- Volání callback funkce
    if currentCallbacks.onComplete then
        currentCallbacks.onComplete(PlayerId())
    end
    
    -- Server event
    TriggerServerEvent('wiring_minigame:completed', PlayerId())
    
    -- Automatické zavření
    if Config.AutoCloseOnComplete then
        Citizen.SetTimeout(Config.CloseDelay, function()
            stopMinigame()
        end)
    end
    
    cb('ok')
end)

-- Callback pro zavření UI
RegisterNUICallback('CloseNui', function(data, cb)
    debugPrint("Minigame closed by user")
    stopMinigame()
    cb('ok')
end)

-- ===========================
-- EXPORTY
-- ===========================

-- Hlavní export pro spuštění minihry
exports('StartWiringMinigame', function(options, callbacks)
    debugPrint("Export StartWiringMinigame called")
    
    -- Nastavení callbacks
    currentCallbacks = mergeTables(Config.DefaultCallbacks, callbacks or {})
    
    -- Spuštění minihry
    return startMinigame(options)
end)

-- Export pro kontrolu stavu minihry
exports('IsMinigameActive', function()
    return isMinigameActive
end)

-- Export pro zastavení minihry
exports('StopMinigame', function()
    return stopMinigame()
end)

-- ===========================
-- UDÁLOSTI
-- ===========================

-- Klávesové zkratky
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        
        if isMinigameActive and Config.AllowEscapeClose then
            -- ESC nebo Backspace pro zavření
            if IsControlJustPressed(0, 322) or IsControlJustPressed(0, 177) then -- ESC nebo Backspace
                stopMinigame()
            end
        end
    end
end)

-- Server eventy
RegisterNetEvent('wiring_minigame:start')
AddEventHandler('wiring_minigame:start', function(options, callbacks)
    exports[Config.ResourceName]:StartWiringMinigame(options, callbacks)
end)

RegisterNetEvent('wiring_minigame:stop')
AddEventHandler('wiring_minigame:stop', function()
    exports[Config.ResourceName]:StopMinigame()
end)

-- ===========================
-- TEST COMMAND
-- ===========================

-- Funkce pro kontrolu oprávnění
local function hasTestPermission(source)
    if not Config.TestCommandPermission then
        return true -- Všichni mohou používat
    end

    if Config.TestCommandPermission == "admin" then
        return IsPlayerAceAllowed(source, 'command') or source == 0
    end

    return IsPlayerAceAllowed(source, Config.TestCommandPermission)
end

-- Registrace test commandu
if Config.EnableTestCommand then
    RegisterCommand(Config.TestCommandName, function(source, args, rawCommand)
        -- Kontrola oprávnění
        if not hasTestPermission(source) then
            TriggerEvent('chat:addMessage', source, {
                color = {255, 0, 0},
                args = {'SYSTEM', Config.TestCommandMessages.no_permission}
            })
            return
        end

        -- Kontrola, zda už minigra neběží
        if exports[Config.ResourceName]:IsMinigameActive() then
            TriggerEvent('chat:addMessage', source, {
                color = {255, 165, 0},
                args = {'WIRING', Config.TestCommandMessages.already_active}
            })
            return
        end

        -- Parsování argumentů
        local wireCount = tonumber(args[1]) or Config.TestCommandDefaults.wire_count
        local colorScheme = args[2] or Config.TestCommandDefaults.colors
        local snapRadius = tonumber(args[3]) or Config.TestCommandDefaults.snap_radius
        local scale = tonumber(args[4]) or Config.TestCommandDefaults.scale

        -- Validace argumentů
        wireCount = math.min(32, math.max(1, wireCount))
        snapRadius = math.min(100, math.max(5, snapRadius))
        scale = math.min(2.0, math.max(0.1, scale))

        -- Validace barevného schématu
        local validColors = {"auto", "classic", "extended", "rainbow"}
        local isValidColor = false
        for _, validColor in ipairs(validColors) do
            if colorScheme == validColor then
                isValidColor = true
                break
            end
        end
        if not isValidColor then
            colorScheme = Config.TestCommandDefaults.colors
        end

        -- Nastavení minihry
        local options = {
            wire_count = wireCount,
            colors = colorScheme,
            snap_radius = snapRadius,
            scale = scale
        }

        -- Callback funkce pro test
        local callbacks = {
            onComplete = function(playerId)
                TriggerEvent('chat:addMessage', source, {
                    color = {0, 255, 0},
                    args = {'WIRING', Config.TestCommandMessages.completed}
                })
                debugPrint("Test minigame completed by player " .. playerId)
            end,

            onFail = function(playerId, reason)
                TriggerEvent('chat:addMessage', source, {
                    color = {255, 0, 0},
                    args = {'WIRING', Config.TestCommandMessages.failed .. " (" .. (reason or "neznámý důvod") .. ")"}
                })
                debugPrint("Test minigame failed by player " .. playerId .. ": " .. (reason or "unknown"))
            end,

            onClose = function(playerId)
                TriggerEvent('chat:addMessage', source, {
                    color = {128, 128, 128},
                    args = {'WIRING', Config.TestCommandMessages.closed}
                })
                debugPrint("Test minigame closed by player " .. playerId)
            end
        }

        -- Spuštění minihry
        local success = exports[Config.ResourceName]:StartWiringMinigame(options, callbacks)

        if success then
            TriggerEvent('chat:addMessage', source, {
                color = {0, 255, 255},
                args = {'WIRING', Config.TestCommandMessages.started}
            })

            -- Zobrazení informací o nastavení
            TriggerEvent('chat:addMessage', source, {
                color = {200, 200, 200},
                args = {'INFO', string.format("Kabely: %d | Barvy: %s | Přichycení: %d | Měřítko: %.1f",
                    wireCount, colorScheme, snapRadius, scale)}
            })
        end

    end, false)

    -- Nápověda pro test command
    TriggerEvent('chat:addSuggestion', '/' .. Config.TestCommandName, 'Spustí test minihry spojování kabelů', {
        { name="wire_count", help="Počet kabelů (1-32, výchozí: " .. Config.TestCommandDefaults.wire_count .. ")" },
        { name="colors", help="Barevné schéma (auto/classic/extended/rainbow, výchozí: " .. Config.TestCommandDefaults.colors .. ")" },
        { name="snap_radius", help="Poloměr přichycení (5-100, výchozí: " .. Config.TestCommandDefaults.snap_radius .. ")" },
        { name="scale", help="Měřítko (0.1-2.0, výchozí: " .. Config.TestCommandDefaults.scale .. ")" }
    })
end

-- ===========================
-- INICIALIZACE
-- ===========================

Citizen.CreateThread(function()
    debugPrint("Wiring Minigame Client initialized")
    debugPrint("Resource name: " .. Config.ResourceName)
    debugPrint("Wire count: " .. Config.WireCount)
    debugPrint("Colors: " .. tostring(Config.Colors))
    debugPrint("Test command enabled: " .. tostring(Config.EnableTestCommand))
    if Config.EnableTestCommand then
        debugPrint("Test command: /" .. Config.TestCommandName)
    end
end)
