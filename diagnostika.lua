-- Diagnostický script pro minihru spojování kabelů

RegisterCommand('wiring_diag', function(source, args)
    print("=== DIAGNOSTIKA WIRING MINIGAME ===")
    
    -- Zkontroluj, zda resource existuje
    local resourceName = GetCurrentResourceName()
    print("Resource name: " .. resourceName)
    
    -- Zkontroluj, zda jsou exporty dostupné
    local exports_available = {}
    
    -- Test StartWiringMinigame export
    local success, result = pcall(function()
        return exports[resourceName]['StartWiringMinigame']
    end)
    exports_available.StartWiringMinigame = success
    print("StartWiringMinigame export: " .. (success and "✅ DOSTUPNÝ" or "❌ NEDOSTUPNÝ"))
    
    -- Test IsMinigameActive export
    success, result = pcall(function()
        return exports[resourceName]['IsMinigameActive']
    end)
    exports_available.IsMinigameActive = success
    print("IsMinigameActive export: " .. (success and "✅ DOSTUPNÝ" or "❌ NEDOSTUPNÝ"))
    
    -- Test StopMinigame export
    success, result = pcall(function()
        return exports[resourceName]['StopMinigame']
    end)
    exports_available.StopMinigame = success
    print("StopMinigame export: " .. (success and "✅ DOSTUPNÝ" or "❌ NEDOSTUPNÝ"))
    
    -- Zkontroluj konfiguraci
    print("\n=== KONFIGURACE ===")
    print("Wire Count: " .. tostring(Config.WireCount))
    print("Colors: " .. tostring(Config.Colors))
    print("Snap Radius: " .. tostring(Config.SnapRadius))
    print("Debug: " .. tostring(Config.Debug))
    print("Test Command Enabled: " .. tostring(Config.EnableTestCommand))
    print("Test Command Name: " .. tostring(Config.TestCommandName))
    
    -- Zkontroluj, zda minihra právě běží
    if exports_available.IsMinigameActive then
        local isActive = exports[resourceName]:IsMinigameActive()
        print("Minihra právě běží: " .. (isActive and "✅ ANO" or "❌ NE"))
    end
    
    -- Zkontroluj soubory
    print("\n=== SOUBORY ===")
    local files_to_check = {
        "html/simple_enhanced.html",
        "html/js/chunk-vendors.d22fa5dc.js",
        "html/css/app.d30a452f.css",
        "html/sound/1.ogg",
        "html/sound/2.ogg"
    }
    
    for _, file in ipairs(files_to_check) do
        -- Toto je jen informativní, nemůžeme skutečně zkontrolovat existenci souboru z Lua
        print("Soubor: " .. file .. " (zkontrolujte manuálně)")
    end
    
    print("\n=== DOPORUČENÍ ===")
    if not exports_available.StartWiringMinigame then
        print("❌ Hlavní export není dostupný - zkontrolujte client.lua")
    end
    
    if not Config.Debug then
        print("⚠️  Debug režim je vypnutý - povolte pro více informací")
    end
    
    if not Config.EnableTestCommand then
        print("⚠️  Test command je vypnutý - povolte pro testování")
    end
    
    print("\n=== DALŠÍ KROKY ===")
    print("1. Zkontrolujte F8 konzoli pro chyby")
    print("2. Zkontrolujte browser console (F12) pro JavaScript chyby")
    print("3. Zkuste spustit: /" .. Config.TestCommandName)
    print("4. Zkontrolujte server logy")
    
    print("=== KONEC DIAGNOSTIKY ===")
end)

print("Diagnostický příkaz zaregistrován: /wiring_diag")
