# Oprava minihry spojování kabelů

## Problém
Minihra spojování kabelů nefungovala - kabely se nedaly spojovat.

## Identifikované problémy
1. **Chybějící GSAP knihovna** - Minihra používala GSAP pro animace a drag & drop, ale knihovna nebyla načtena
2. **Chybějící Draggable plugin** - Plugin pro přetahování nebyl dostupný
3. **Nesprávné výpočty pozic** - Pozice cílů pro kabely byly špatně vypočítané
4. **Chyb<PERSON>j<PERSON><PERSON><PERSON> error handling** - Ž<PERSON><PERSON><PERSON> ošetření chyb při nedostupnosti knihoven

## Provedené opravy

### 1. Přidání GSAP knihoven
Do `html/simple_enhanced.html` byly přidány CDN odkazy na GSAP:
```html
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/Draggable.min.js"></script>
```

### 2. Oprava drag & drop funkcionalnosti
- Přepsána funkce `setupDraggables()` pro lepší kompatibilitu
- Opraveny výpočty pozic při přetahování
- Přidáno error handling pro případ nedostupnosti GSAP

### 3. Oprava pozicování kabelů
- Opraveny výpočty `targetX` a `targetY` v `generateRandomConfiguration()`
- Zlepšena detekce kolize při spojování kabelů

### 4. Povolení debug režimu
V `config.lua` byl povolen debug režim pro lepší diagnostiku:
```lua
Config.Debug = true
```

## Testování

### Základní test
Použijte příkaz v hře:
```
/wiring_test
```

### Pokročilé testování
Můžete použít test soubor `test_minigame.lua`:
```
/test_wiring - základní test
/test_wiring_hard - těžká verze (6 kabelů)
/test_wiring_rainbow - rainbow verze (8 kabelů)
```

## Možné další problémy

### Pokud minihra stále nefunguje:

1. **Zkontrolujte konzoli F8** - měly by se zobrazit debug zprávy
2. **Zkontrolujte browser console** (F12) - mohou tam být JavaScript chyby
3. **Ověřte načtení resource** - ujistěte se, že resource je správně spuštěn
4. **Zkontrolujte fxmanifest.lua** - ujistěte se, že všechny soubory jsou správně zahrnuty

### Fallback řešení
Pokud GSAP nefunguje, minihra má základní fallback funkcionalnost bez animací.

## Kontakt
Pokud problémy přetrvávají, zkontrolujte:
- Server logy
- Client logy (F8)
- Browser console (F12)
- Síťové připojení (CDN odkazy na GSAP)
